import { ApiListResponse, ApiResponse } from "../types/api";
import type {
  InvoiceInfoItem,
  InvoiceSearchParams,
  PostPaidInvoiceItem,
  PostPaidInvoiceSearchParams,
  AccountSeqSimpleInfo,
} from "../types/invoice";
import api from "./api";

export const getInvoiceList = async (
  params: InvoiceSearchParams
): Promise<ApiListResponse<InvoiceInfoItem[]>> => {
  const response = await api.get("/invoice-info", { params });
  return response.data;
};

export const createInvoice = async (data: any): Promise<any> => {
  const response = await api.post("/invoice-info", data);
  return response.data;
};

export const updateInvoice = async (
  id: number,
  data: any
): Promise<any> => {
  const response = await api.put(`/invoice-info/${id}`, data);
  return response.data;
};

export const getInvoiceDetail = async (
  id: number
): Promise<ApiResponse<InvoiceInfoItem>> => {
  const response = await api.get<ApiResponse<InvoiceInfoItem>>(
    `/invoice-info/${id}`
  );
  return response.data;
};

// 获取分账序号简单列表
export const getAccountSeqSimpleList = async (
  customer_num?: string
): Promise<ApiResponse<AccountSeqSimpleInfo[]>> => {
  const params = customer_num ? { customer_num } : {};
  const response = await api.get("/account-seq/simple-list", { params });
  return response.data;
};

// 获取后付费发票列表
export const getPostPaidInvoiceList = async (
  params: PostPaidInvoiceSearchParams
): Promise<ApiListResponse<PostPaidInvoiceItem[]>> => {
  const response = await api.get("/post-paid-invoice", { params });
  return response.data;
};

// 批量预开票请求数据接口
export interface BatchPreInvoiceRequest {
  customer_num: string;
  account_seq: string;
  start_charge_month: number;
  end_charge_month: number;
}

// 批量预开票API
export const batchPreInvoice = async (
  data: BatchPreInvoiceRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post("/post-paid-invoice/batch-issuance", data);
  return response.data;
};
